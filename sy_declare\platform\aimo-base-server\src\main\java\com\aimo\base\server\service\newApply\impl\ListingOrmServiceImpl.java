package com.aimo.base.server.service.newApply.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.aimo.base.client.constants.BaseConstants;
import com.aimo.base.client.model.base.BaseDepartment;
import com.aimo.base.client.model.base.BaseUser;
import com.aimo.base.client.model.basf.BaseProduct;
import com.aimo.base.client.model.basf.BaseProductDetail;
import com.aimo.base.client.model.basf.BaseShop;
import com.aimo.base.client.model.newApply.ListingOrm;
import com.aimo.base.client.model.newApply.NewApply;
import com.aimo.base.client.model.newApply.NewApplyDetail;
import com.aimo.base.client.model.overseasSend.TransparentSku;
import com.aimo.base.client.param.newApply.ListingOrmParam;
import com.aimo.base.server.controller.custom.CustomModule;
import com.aimo.base.server.feign.lingxing.LingxingListingClient;
import com.aimo.base.server.feign.ys.YsProductBaseClient;
import com.aimo.base.server.feign.ys.YsProductBomClient;
import com.aimo.base.server.mapper.newApply.ListingOrmMapper;
import com.aimo.base.server.service.base.BaseDepartmentService;
import com.aimo.base.server.service.base.BaseUserService;
import com.aimo.base.server.service.basf.BaseProductService;
import com.aimo.base.server.service.basf.BaseShopService;
import com.aimo.base.server.service.handler.BaseHandler;
import com.aimo.base.server.service.newApply.ListingOrmService;
import com.aimo.base.server.service.overseasSend.TransparentSkuService;
import com.aimo.common.exception.OpenAlertException;
import com.aimo.common.model.ResultBody;
import com.aimo.common.mybatis.base.entity.IdEntity;
import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.security.OpenHelper;
import com.aimo.common.utils.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sy.lingxing.client.model.listing.LxListing;
import com.sy.ys.client.model.base.YsProduct;
import com.sy.ys.client.model.base.YsProductBom;
import com.sy.ys.client.param.base.YsProductBomDetailParam;
import com.sy.ys.client.param.base.YsProductBomParam;
import com.sy.ys.client.param.base.YsProductParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ListingOrmServiceImpl extends BaseServiceImpl<ListingOrmMapper, ListingOrm> implements ListingOrmService {
    @Resource
    private ListingOrmMapper listingOrmMapper;
    @Resource
    private BaseShopService baseShopService;
    @Resource
    private BaseProductService baseProductService;
    @Resource
    private BaseUserService baseUserService;
    @Resource
    private BaseDepartmentService baseDepartmentService;
    @Resource
    private LingxingListingClient listingClient;
    @Resource
    private YsProductBaseClient ysProductBaseClient;
    @Resource
    private YsProductBomClient ysProductBomClient;
    @Resource
    private TransparentSkuService transparentSkuService;
    @Override
    public Long getPredictionAsinId(Long id, String sellerSku){
        return listingOrmMapper.getPredictionAsinId(id,sellerSku);
    }
    @Override
    public IPage<ListingOrm> listPage(ListingOrmParam param) {
        IPage<ListingOrm> iPage = listingOrmMapper.selectPages(new Page<>(param.getPage(), param.getLimit()), param);
        Map<Long, Map<String, ListingOrm>> ormMap = new HashMap<>();
        iPage.getRecords().forEach(item -> ormMap.computeIfAbsent(item.getShopId(), k -> new HashMap<>()).put(item.getSellerSku(), item));
        ormMap.forEach((shopId, eachMap) -> {
            QueryWrapper<TransparentSku> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TransparentSku::getShopId, shopId).in(TransparentSku::getSellerSku, eachMap.keySet()).eq(TransparentSku::getStatus, BaseConstants.STATUS0);
            transparentSkuService.list(queryWrapper).forEach(item -> {
                ListingOrm orm = eachMap.get(item.getSellerSku());
                orm.setUpcCode(item.getEan());
            });
        });
        List<Long> mainIdList = iPage.getRecords().stream().map(ListingOrm::getMainId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(mainIdList)){
            param = new ListingOrmParam();
            param.setLimit(-1);
            param.setIdList(mainIdList);
            Map<Long,ListingOrm> ormMap2 = listingOrmMapper.selectPages(new Page<>(param.getPage(), param.getLimit()),param).getRecords().stream()
                    .collect(Collectors.toMap(IdEntity::getId, item->new ListingOrm(item.getShopName(),item.getSellerSku(),item.getFnsku())));
            iPage.getRecords().stream().filter(item->item.getMainId() != null).forEach(item->item.setListingOrm(ormMap2.get(item.getMainId())));
        }
        return iPage;
    }
    public Map<String,ListingOrm> listListingOrm(Long shopId,String listingTitle){
        List<ListingOrm> ormList = listingOrmMapper.listListingOrm(shopId,listingTitle);
        Map<String,ListingOrm> ormMap = new HashMap<>();
        ormList.forEach(item->{
            String key = StringUtils.concat(",",Arrays.asList(item.getProductName(),StringUtils.toMyString(item.getProductInseam(),StringUtils.emptyStr),item.getProductColor(),item.getProductSize()));
            if(!ormMap.containsKey(key)){
                ormMap.put(key,item);
            }
            ListingOrm old = ormMap.get(key);
            if(Objects.equals(old.getMainId(),item.getMainId())){//主销售SKU就替换
                ormMap.put(key,item);
                return;
            }
            if(Objects.equals(old.getId(),old.getMainId())){//自己是主的就不替换
                return;
            }
            if(old.getCreateTime().compareTo(item.getCreateTime())<0){//不涉及主的，就替换成最新的
                ormMap.put(key,item);
            }
        });
        return ormMap;
    }
    @Override
    public void exportFile(HttpServletResponse response, ListingOrmParam param) {
        param.setPage(1);
        param.setLimit(-1);
        IPage<ListingOrm> page = this.listPage(param);
        ExportParams exportParams = new ExportParams();
        exportParams.setDataHandler(new BaseHandler<>());
        exportParams.setStyle(ExcelStyleUtil.class);
        exportParams.setSheetName("销售对照表");
        try {
            page.getRecords().stream().filter(item->item.getListingOrm() != null).forEach(item->{
                item.setShopNameMain(item.getListingOrm().getShopName());
                item.setSellerSkuMain(item.getListingOrm().getSellerSku());
                item.setFnskuMain(item.getListingOrm().getFnsku());
            });
            AtomicInteger index = new AtomicInteger(0);
            page.getRecords().forEach(item->item.setIndex(index.getAndIncrement()));
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ListingOrm.class, page.getRecords());
            ExcelUtil.export(response, workbook, "销售对照表" + DateUtils.getCurrentDate().getTime() + ".xls");
        } catch (Exception e) {
            throw new OpenAlertException("导出销售对照表失败");
        }
    }

    @Override
    @Transactional
    public ListingOrm createListingOrm(NewApply newApply, NewApplyDetail detail, LxListing lxListing) {
        ListingOrm listingOrm = new ListingOrm();
        listingOrm.setSellerId(newApply.getSellerId());
        listingOrm.setShopId(newApply.getShopId());
        listingOrm.setListingTitle(newApply.getListingTitle());
        listingOrm.setSellerSku(detail.getSellerSku());
        listingOrm.setSkuLoc(newApply.getSkuLoc());
        copyListingProp(lxListing, listingOrm);
        listingOrm.setProductId(detail.getProductId());
        listingOrm.setRemark(newApply.getSheetDate() + "上新申请");
        Long userId = OpenHelper.getUserId();
        listingOrm.setCreateUser(StringUtils.toLong(userId, newApply.getCreateUser()));
        return listingOrm;
    }

    private void setClassName(LxListing lxListing, ListingOrm listingOrm) {
        if (!StringUtils.isEmpty(lxListing.getCategory()) && StringUtils.isEmpty(listingOrm.getClassName())) {
            int start = lxListing.getCategory().lastIndexOf(">>");
            int end = lxListing.getCategory().lastIndexOf("(");
            listingOrm.setClassName(lxListing.getCategory().substring(start == -1 ? 0 : (start + 2), end == -1 ? lxListing.getCategory().length() : end));
        }
    }

    @Override
    public Boolean importFile(MultipartFile importFile) {
        ImportParams importParams = new ImportParams();
        importParams.setDataHandler(new BaseHandler<>());
        importParams.setTitleRows(0);
        ExcelImportResult<ListingOrm> importResult;
        try {
            importResult = ExcelImportUtil.importExcelMore(importFile.getInputStream(), ListingOrm.class, importParams);
            if (importResult.isVerfiyFail()) {
                throw new OpenAlertException("文件格式错误，请检查文件");
            }
            List<ListingOrm> ormList = importResult.getList();
            checkImportData(ormList);
            ormList.forEach(this::saveObj);
            checkMainId(ormList);
            ormList.stream().filter(item->item.getMainId() != null).forEach(item->{
                UpdateWrapper<ListingOrm> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().set(ListingOrm::getMainId,item.getMainId()).eq(ListingOrm::getId,item.getId());
                listingOrmMapper.update(null,updateWrapper);
            });
            return true;
        } catch (Exception e) {
            throw new OpenAlertException(e.getMessage());
        }
    }


    private ListingOrm getBy(Long shopId, String sellerSku,String fnsku) {
        if (shopId == null || StringUtils.isEmpty(sellerSku)) {
            return null;
        }
        QueryWrapper<ListingOrm> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ListingOrm::getShopId, shopId)
                .eq(ListingOrm::getSellerSku, sellerSku)
                .eq(StringUtils.isNotEmpty(fnsku),ListingOrm::getFnsku, fnsku)
                .isNull(StringUtils.isEmpty(fnsku),ListingOrm::getFnsku);
        return listingOrmMapper.selectOne(queryWrapper);
    }

    public void checkImportData(List<ListingOrm> listingOrmList) {
        checkShop(listingOrmList);
        checkListing(listingOrmList);
        checkProduct(listingOrmList);
        checkSellerId(listingOrmList);
        Long userId = OpenHelper.getUserId();
        Date current = DateUtils.getCurrentDate();
        listingOrmList.forEach(item->{
            item.setUpdateUser(userId);
            item.setUpdateTime(current);
        });
    }
    private void checkMainId(List<ListingOrm> listingOrmList) {
        Map<String,List<ListingOrm>> keyOrmMap = new HashMap<>();
        listingOrmList.stream().filter(item->!StringUtils.isEmpty(item.getShopNameMain()) && !StringUtils.isEmpty(item.getSellerSkuMain()))
                .forEach(item->keyOrmMap.computeIfAbsent(StringUtils.concat(StringUtils.commaSp,item.getListingOrm().getShopId(),item.getListingOrm().getSellerSku(),
                        StringUtils.toMyString(item.getListingOrm().getFnsku(),StringUtils.EMPTY)),k->new ArrayList<>()).add(item));
        listingOrmMapper.listListingOrmByKeyList(new ArrayList<>(keyOrmMap.keySet())).forEach(item->
                keyOrmMap.get(StringUtils.concat(StringUtils.commaSp,item.getShopId(),item.getSellerSku(),
                StringUtils.toMyString(item.getFnsku(),StringUtils.EMPTY))).forEach(each->each.setMainId(item.getId())));
    }

    private void checkSellerId(List<ListingOrm> listingOrmList) {
        List<String> sellerNameList = listingOrmList.stream().map(ListingOrm::getSellerName).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerNameList)) {
            return;
        }
        QueryWrapper<BaseUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BaseUser::getNickName, sellerNameList).select(BaseUser::getNickName, BaseUser::getId);
        Map<String, Long> idMap = new HashMap<>();
        baseUserService.list(queryWrapper).forEach(item -> idMap.put(item.getNickName(), item.getId()));
        listingOrmList.forEach(item -> item.setSellerId(idMap.get(item.getSellerName())));
    }

    private void checkProduct(List<ListingOrm> listingOrmList) {
        if (CollectionUtils.isEmpty(listingOrmList)) {
            return;
        }
        List<String> productCodeList = listingOrmList.stream().map(ListingOrm::getProductCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodeList)) {
            return;
        }
        QueryWrapper<BaseProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BaseProduct::getCode, productCodeList).select(BaseProduct::getCode, BaseProduct::getId);
        Map<String, Long> idMap = baseProductService.list(queryWrapper).stream().collect(Collectors.toMap(BaseProduct::getCode, BaseProduct::getId));
        listingOrmList.forEach(item -> item.setProductId(idMap.get(item.getProductCode())));
    }

    private void checkListing(List<ListingOrm> listingOrmList) {
        if (CollectionUtils.isEmpty(listingOrmList)) {
            return;
        }
        Map<Long,Long> sidMap = baseShopService.list().stream().filter(item->item.getSid() !=null).collect(Collectors.toMap(IdEntity::getId, BaseShop::getSid,(o1, o2)->o1));
        Map<Long, Map<String, ListingOrm>> shopOrmMap = new HashMap<>();
        listingOrmList.forEach(item -> shopOrmMap.computeIfAbsent(item.getShopId(), k -> new HashMap<>()).put(item.getSellerSku(), item));
        shopOrmMap.forEach((shopId, sellerSkuOrmMap) -> {
            List<String> sellerSkuList = new ArrayList<>(sellerSkuOrmMap.keySet());
            Lists.splitList(sellerSkuList,50).forEach(childList->{
                List<LxListing> lxListingList = listingClient.queryListing(sidMap.get(shopId),JSON.toJSONString(childList));
                if(CollectionUtils.isEmpty(lxListingList)){
                    return;
                }
                lxListingList.forEach(lxListing -> {
                    ListingOrm listingOrm = sellerSkuOrmMap.get(lxListing.getSellerSku());
                    copyListingProp(lxListing, listingOrm);
                });
            });
        });
    }

    private void copyListingProp(LxListing lxListing, ListingOrm listingOrm) {
        setClassName(lxListing, listingOrm);
        listingOrm.setFulfillmentType(lxListing.getFulfillmentType());
        listingOrm.setFnsku(lxListing.getFnsku());
        listingOrm.setAsin(lxListing.getAsin());
        listingOrm.setParentAsin(lxListing.getParentAsin());
    }

    private void checkShop(List<ListingOrm> listingOrmList) {
        if (CollectionUtils.isEmpty(listingOrmList)) {
            return;
        }
        Map<String, Long> shopNameMap = baseShopService.list().stream().filter(item->Objects.equals(item.getStatus(),BaseConstants.STATUS0)).collect(Collectors.toMap(BaseShop::getName, BaseShop::getId));
        Long userId = OpenHelper.getUserId();
        Date current = DateUtils.getCurrentDate();
        listingOrmList.forEach(item -> {
            if (StringUtils.isEmpty(item.getShopName()) || !shopNameMap.containsKey(item.getShopName())) {
                throw new OpenAlertException("网店信息不存在"+item.getShopName());
            }
            item.setShopId(shopNameMap.get(item.getShopName()));
            item.setCreateTime(current);
            item.setCreateUser(userId);
            if(StringUtils.isEmpty(item.getShopNameMain()) || !shopNameMap.containsKey(item.getShopNameMain()) || StringUtils.isEmpty(item.getSellerSkuMain())){
                throw new OpenAlertException("主销售网店信息不存在"+item.getShopName());
            }
            item.setListingOrm(new ListingOrm(shopNameMap.get(item.getShopNameMain()),item.getSellerSkuMain(),item.getFnskuMain()));
        });
    }
    public boolean saveObj(ListingOrm listingOrm) {
        ListingOrm old = getBy(listingOrm.getShopId(), listingOrm.getSellerSku(),listingOrm.getFnsku());
        if (old == null) {
            listingOrm.setSyncLx(BaseConstants.STATUS0);
            listingOrm.setSyncYs(BaseConstants.STATUS0);
            listingOrm.setCreateUser(listingOrm.getUpdateUser());
            listingOrm.setCreateTime(listingOrm.getUpdateTime());
            listingOrmMapper.insert(listingOrm);
            this.saveNoTransactionLog("新增", "新增维护对照表" + listingOrm, CustomModule.NEW_APPLY_LISTING_ORM, listingOrm.getId());
        } else {
            listingOrm.setId(old.getId());
            listingOrm.setCreateTime(old.getCreateTime());
            listingOrm.setCreateUser(old.getCreateUser());
            if (!Objects.equals(listingOrm.getSellerId(), old.getSellerId())) {
                listingOrm.setSyncLx(BaseConstants.STATUS0);
            }
            if (!Objects.equals(listingOrm.getProductId(), old.getProductId())) {
                listingOrm.setSyncLx(BaseConstants.STATUS0);
                listingOrm.setSyncYs(BaseConstants.STATUS0);
            }
            listingOrmMapper.updateById(listingOrm);
            this.saveNoTransactionLog("更新", "更新维护对照表" + listingOrm, CustomModule.NEW_APPLY_LISTING_ORM, listingOrm.getId());
        }
        return true;
    }

    public boolean syncYsProduct(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        ListingOrmParam param = new ListingOrmParam();
        param.setIdList(idList);
        param.setLimit(-1);
        List<ListingOrm> ormList = this.listPage(param).getRecords();
        List<String> errorList = new ArrayList<>();
        ormList.forEach(item -> {
            try {
                BaseProduct baseProduct = baseProductService.getBy(item.getProductId(), null);
                YsProduct mainYsProduct = getMainYsProduct(baseProduct);
                List<YsProduct> ysProductList = ysProductBaseClient.listByCode(Collections.singletonList(item.getFnsku()));
                YsProductParam productParam = new YsProductParam(null,item.getFnsku(), item.getShopName(), item.getListingTitle(), item.getSellerSku(), item.getProductName(), mainYsProduct.getSy17(), mainYsProduct.getSy14(),null);
                if (CollectionUtils.isEmpty(ysProductList)) {//FNSKU的物料不存在
                    ResultBody<Long> resultBody = ysProductBaseClient.addYsProduct(productParam);
                    if (!resultBody.isOk()) {
                        throw new OpenAlertException(resultBody.getMessage());
                    }
                    this.saveNoTransactionLog("同步", "新增维护对照表物料到用友" + item, CustomModule.NEW_APPLY_LISTING_ORM, item.getId());
                }else if (!ysProductList.get(0).checkPlatformProduct(productParam)) {
                    YsProduct selfYsProduct =ysProductList.get(0);
                    productParam.setId(selfYsProduct.getId());
                    productParam.setDefId(selfYsProduct.getDefId());
                    ResultBody<Long> resultBody = ysProductBaseClient.addYsProduct(productParam);
                    if (!resultBody.isOk()) {
                        throw new OpenAlertException(resultBody.getMessage());
                    }
                    this.saveNoTransactionLog("同步", "更新维护对照表物料到用友" + item, CustomModule.NEW_APPLY_LISTING_ORM, item.getId());
                }
                ResultBody<List<YsProductBom>> resultBody = ysProductBomClient.listBomBy(item.getFnsku());
                if (!resultBody.isOk() || CollectionUtils.isEmpty(resultBody.getData())) {
                    YsProductBomParam bomParam = new YsProductBomParam(item.getId(), item.getFnsku());
                    bomParam.setDetailList(new ArrayList<>());
                    if (CollectionUtils.isEmpty(baseProduct.getDetailList())) {
                        bomParam.getDetailList().add(new YsProductBomDetailParam(baseProduct.getCode(), 1));
                    } else {
                        baseProduct.getDetailList().forEach(detail -> bomParam.getDetailList().add(new YsProductBomDetailParam(detail.getChildSku(), detail.getQuantity())));
                    }
                    ResultBody<Long> addBomResultBody = ysProductBomClient.addYsProductBom(bomParam);
                    if (!addBomResultBody.isOk()) {
                        throw new OpenAlertException(addBomResultBody.getMessage());
                    }
                    this.saveNoTransactionLog("同步", "新增维护对照表物料清单到用友" + item, CustomModule.NEW_APPLY_LISTING_ORM, item.getId());
                }
                item.setSyncYs(BaseConstants.STATUS1);
                listingOrmMapper.updateById(item);
            } catch (Exception e) {
                errorList.add("对照表：网店：" + item.getShopName() + ",销售SKU：" + item.getSellerSku() + ",原因：" + e.getMessage());
            }
        });
        ResultBody<Boolean> resultBody = ysProductBomClient.auditYsProductBom();
        if (!resultBody.isOk()) {
            errorList.add("用友物料清单审核失败");
        }
        if (errorList.isEmpty()) {
            return true;
        } else {
            throw new OpenAlertException(StringUtils.concat(StringUtils.commaSp, errorList));
        }
    }

    private YsProduct getMainYsProduct(BaseProduct baseProduct) {
        String productCode = baseProduct.getCode();
        if (!CollectionUtils.isEmpty(baseProduct.getDetailList())) {
            Optional<BaseProductDetail> optional = baseProduct.getDetailList().stream().filter(detail -> (!StringUtils.contains(detail.getChildSpec(), "UC00"))).findFirst();
            if (optional.isPresent()) {
                productCode = optional.get().getChildSku();
            } else {
                productCode = baseProduct.getDetailList().get(0).getChildSku();
            }
        }
        List<YsProduct> ysProductList = ysProductBaseClient.listByCode(Collections.singletonList(productCode));
        if (CollectionUtils.isEmpty(ysProductList)) {
            throw new OpenAlertException("用友的物料编码" + productCode + "不存在");
        }
        return ysProductList.get(0);
    }

    public boolean syncLx(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        ListingOrmParam param = new ListingOrmParam();
        param.setIdList(idList);
        param.setLimit(-1);
        List<ListingOrm> ormList = this.listPage(param).getRecords();
        List<BaseShop> baseShopList = baseShopService.list().stream().filter(item -> !Objects.equals(item.getStatus(), BaseConstants.STATUS1) && item.getSid() !=null).collect(Collectors.toList());
        Map<Long, BaseShop> shopMap = baseShopList.stream().filter(item -> item.getPlatformId().equals(BaseConstants.PLATFORM_AMAZON)).collect(Collectors.toMap(BaseShop::getId, Function.identity()));
        ormList.stream().filter(item->shopMap.containsKey(item.getShopId())).forEach(item->this.syncLx(item,shopMap));
        return true;
    }
    private void syncLx(ListingOrm listingOrm,Map<Long, BaseShop> shopMap) {
        Long sid = shopMap.get(listingOrm.getShopId()).getSid();
        List<LxListing> lxListingList = listingClient.queryAsin(sid,listingOrm.getAsin(),listingOrm.getSellerSku(),true);
        Long sellerId = listingOrm.getSellerId();
        if(sellerId != null){
            List<Long> userList = new ArrayList<>();
            userList.add(sellerId);
            Long departmentId = baseUserService.getUserById(sellerId).getDepartmentId();
            if(departmentId != null){
                BaseDepartment baseDepartment = baseDepartmentService.getById(departmentId);
                if(baseDepartment != null){
                    Long managerId = baseDepartment.getManagerId();
                    userList.add(managerId);
                }
            }
            Map<Long, BaseUser> userMap = new HashMap<>();
            baseUserService.listByIds(userList).forEach(item -> userMap.put(item.getId(), item));
            String sellerName = getSellerName(sellerId,userMap);
            lxListingList.stream().map(LxListing::getSid).distinct().forEach(lxSid->{
                List<Map<String, String>> productLinkList = new ArrayList<>();
                Map<String, String> productLink = new HashMap<>();
                productLink.put("asin", listingOrm.getAsin());
                productLink.put("sellerName", sellerName);
                productLinkList.add(productLink);
                Map<Integer,Object> resultMap = listingClient.updatePrincipal(lxSid, JSON.toJSONString(productLinkList));
                Object obj = resultMap.get(0);
                BaseShop baseShop = shopMap.values().stream().filter(item-> Objects.equals(item.getSid(), lxSid)).findFirst().orElse(null);
                String shopName = "";
                if(baseShop != null){
                    shopName =baseShop.getName();
                }
                this.saveNoTransactionLog("设置领星负责人", "同步领星网店"+shopName+"ASIN:"+listingOrm.getAsin()+"设置负责人"+listingOrm.getSellerName()+",结果：" + (obj.equals(true) ? "成功" : obj), CustomModule.NEW_APPLY_LISTING_ORM, listingOrm.getId());
                if(Objects.equals(lxSid, sid) && obj.equals(true)){
                    listingOrm.setSyncLx((listingOrm.getSyncLx()& 2) + 1);
                    listingOrmMapper.updateById(listingOrm);
                }
            });
        }
        lxListingList.forEach(item->{
            List<Map<String, String>> productLinkList = new ArrayList<>();
            Map<String, String> productLink = new HashMap<>();
            productLink.put("sellerSku", item.getSellerSku());
            productLink.put("sku", listingOrm.getProductCode());
            productLinkList.add(productLink);
            Map<Integer, Object> resultMap = listingClient.productLink(item.getSid(), JSON.toJSONString(productLinkList));
            Object obj = resultMap.get(0);
            this.saveNoTransactionLog("设置领星产品对照", "同步领星产品对照网店：" + item.getShopName() + "销售SKU" + item.getSellerSku() + "物料编码：" + listingOrm.getProductCode() + ",结果：" + (obj.equals(true) ? "成功" : obj), CustomModule.NEW_APPLY_LISTING_ORM, listingOrm.getId());
            if(Objects.equals(item.getSid(), sid) && obj.equals(true) && item.getSellerSku().equals(listingOrm.getSellerSku())){
                listingOrm.setSyncLx((listingOrm.getSyncLx()& 1) + 2);
                listingOrmMapper.updateById(listingOrm);
            }
        });
    }
    private String getSellerName(Long sellerId, Map<Long, BaseUser> userMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(userMap.remove(sellerId).getNickName()).append(",");
        if (!userMap.isEmpty()) {
            userMap.forEach((key,value)-> sb.append(value.getNickName()).append(","));
        }
        if (sb.indexOf("王子成") < 0) {//姚林的
            sb.append("王子成,");
        } else {//王子成的
            sb.append("17部,");
        }
        sb.append("谢凯波");
        return sb.toString();
    }


}
